.customButtons {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--ifm-color-emphasis-200);
    /* 确保按钮跟随 TOC 的定位行为 */
    position: sticky;
    bottom: 0;
    background: var(--ifm-background-color);
    z-index: 1;
}

.buttonGroup {
    display: flex;
    flex-direction: column;
    gap: 0.8rem;
}

.button {
    display: flex;
    align-items: center;
    padding: 0.5rem 0.75rem;
    background: var(--ifm-color-emphasis-100);
    border: 1px solid var(--ifm-color-emphasis-300);
    border-radius: 0.375rem;
    /* color: var(--ifm-color-content); */
    color: dodgerblue;
    text-decoration: none;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.button:hover {
    background: var(--ifm-color-emphasis-200);
    border-color: var(--ifm-color-emphasis-400);
    text-decoration: none;
}

.shareRow {
    display: flex;
    align-items: flex-start;
    flex-direction: row;
}

.shareIcons {
    display: flex;
    flex-wrap: wrap;
    /* 超出宽度自动换行 */
    gap: 6px;
    /* 图标之间的间距 */
    border-radius: 4;

}

.shareIcon {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px;
    cursor: pointer;
}

.shareIcon:hover {
    background: var(--ifm-color-emphasis-300);
    border-color: var(--ifm-color-emphasis-500);
}

/* 响应式处理 */
@media (max-width: 996px) {
    .customButtonsFixed {
        display: none;
        /* 在小屏幕上隐藏 */
    }
}