import { useState, useEffect } from 'react';
import styles from './MentalModalTools.module.css';
import { useDoc, useSidebarBreadcrumbs } from '@docusaurus/plugin-content-docs/client';
import { FaTwitter, FaFacebook, FaLinkedin, FaReddit, FaWhatsapp } from "react-icons/fa";
import { MdEmail } from "react-icons/md";

export default function MentalModalTools() {
  const { metadata } = useDoc();
  const breadcrumbs = useSidebarBreadcrumbs();

  const getDomainName = () => {
    if (typeof window === 'undefined') return ''; // SSR 安全检查

    const hostname = window.location.hostname;
    // 移除 www 前缀
    return hostname.replace(/^www\./, '');
  };

  const domainName = getDomainName();
  // 检查当前文档是否属于目标目录
  const isMentalModalPage = metadata.permalink.startsWith('/thinking-matters/classic-mental-models/');

  const handleMobileShare = () => {
    if (navigator.share) {
      navigator.share({
        title: document.title,
        url: window.location.href,
      }).catch(err => console.log('Error sharing:', err));
    }
  };

  const shareToSocial = (platform) => {
    const url = encodeURIComponent(window.location.href);
    const title = encodeURIComponent(breadcrumbs.find(item => item.type === 'link')?.label);

    const shareUrls = {
      twitter: `https://twitter.com/intent/tweet?url=${url}&text=${title}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${url}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${url}`,
      reddit: `https://reddit.com/submit?url=${url}&title=${title}`,
      email: `mailto:?subject=${title}&body=${url}`,
      whatsapp: `https://wa.me/?text=${title}%20${url}`
    };

    if (platform === 'email') {
      window.location.href = shareUrls[platform];
    } else {
      window.open(shareUrls[platform], '_blank', 'width=600,height=400');
    }
  };

  return (
    <div className={styles.customButtons}>
      <div className={styles.buttonGroup}>
        {
          isMentalModalPage && (
            <a
              className={styles.button}
              href={`/aitools/mindkit?mental_model=${encodeURIComponent(breadcrumbs.find(item => item.type === 'link')?.label)}`}
              target='_blank'
            >
              {`Apply '${breadcrumbs.find(item => item.type === 'link')?.label}' with MindKit`}
            </a>
          )
        }
        <a
          className={styles.button}
          href={`https://app.${domainName}/#/aiflow?queryType=link&url=${encodeURIComponent(window.location.href)}`}
          target='_blank'
        >
          Read with FunBlocks Mindmap
        </a>

        {/* 分享区域 */}

        <div className={styles.shareRow}>
          <span className={styles.shareLabel}>Share:&nbsp;</span>
          <div className={styles.shareIcons}>
            <div
              className={styles.shareIcon}
              onClick={() => shareToSocial('twitter')}
              title="Share on Twitter"
            >
              <FaTwitter />
            </div>
            <div
              className={styles.shareIcon}
              onClick={() => shareToSocial('facebook')}
              title="Share on Facebook"
            >
              <FaFacebook />
            </div>
            <div
              className={styles.shareIcon}
              onClick={() => shareToSocial('linkedin')}
              title="Share on LinkedIn"
            >
              <FaLinkedin />
            </div>
            <div
              className={styles.shareIcon}
              onClick={() => shareToSocial('reddit')}
              title="Share on Reddit"
            >
              <FaReddit />
            </div>
            <div
              className={styles.shareIcon}
              onClick={() => shareToSocial('whatsapp')}
              title="Share on WhatsApp"
            >
              <FaWhatsapp />
            </div>
            <div
              className={styles.shareIcon}
              onClick={() => shareToSocial('email')}
              title="Share via Email"
            >
              <MdEmail />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}